#!/usr/bin/env python3
"""
Helper script to set up Gemini API key.
"""
import os

def setup_gemini_api():
    print("Gemini API Setup")
    print("=" * 40)
    
    print("\nTo use Gemini API, you need to:")
    print("1. Get a free API key from Google AI Studio")
    print("2. Set the GEMINI_API_KEY environment variable")
    
    print("\nSteps to get your API key:")
    print("1. Go to: https://makersuite.google.com/app/apikey")
    print("2. Sign in with your Google account")
    print("3. Click 'Create API Key'")
    print("4. Copy the generated API key")
    
    # Check if API key is already set
    existing_key = os.getenv('GEMINI_API_KEY')
    if existing_key:
        print(f"\n✅ GEMINI_API_KEY is already set: {existing_key[:10]}...")
        return
    
    print("\nOnce you have your API key:")
    api_key = input("Enter your Gemini API key (or press Enter to skip): ").strip()
    
    if api_key:
        # Add to .env file
        env_content = ""
        if os.path.exists('.env'):
            with open('.env', 'r') as f:
                env_content = f.read()
        
        # Check if GEMINI_API_KEY already exists in .env
        if 'GEMINI_API_KEY' not in env_content:
            with open('.env', 'a') as f:
                if env_content and not env_content.endswith('\n'):
                    f.write('\n')
                f.write(f'GEMINI_API_KEY={api_key}\n')
        
        print("\n✅ API key added to .env file")
        print("To use it in your current session, run:")
        print(f"export GEMINI_API_KEY={api_key}")
        
        # Set for current session
        os.environ['GEMINI_API_KEY'] = api_key
        print("✅ API key set for current session")
        
    else:
        print("\nTo set the API key manually:")
        print("export GEMINI_API_KEY=your_api_key_here")
        print("or add it to your .env file:")
        print("echo 'GEMINI_API_KEY=your_api_key_here' >> .env")

if __name__ == "__main__":
    setup_gemini_api()
