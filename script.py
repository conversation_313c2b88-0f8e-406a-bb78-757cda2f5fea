import os
import sys
from pathlib import Path

def setup_credentials():
    """Set up Google Cloud credentials if not already configured."""
    # Check if credentials are already set
    if os.getenv('GOOGLE_APPLICATION_CREDENTIALS'):
        return True

    # Look for credential file in current directory
    possible_files = [
        'google-cloud-key.json',
        'service-account-key.json',
        'credentials.json'
    ]

    for file_name in possible_files:
        if os.path.exists(file_name):
            os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = file_name
            print(f"Using credentials from: {file_name}")
            return True

    # Check if gcloud is authenticated
    try:
        import subprocess
        result = subprocess.run(['gcloud', 'auth', 'list', '--filter=status:ACTIVE'],
                              capture_output=True, text=True)
        if result.returncode == 0 and result.stdout.strip():
            print("Using gcloud default credentials")
            return True
    except FileNotFoundError:
        pass

    print("ERROR: No Google Cloud credentials found!")
    print("Please run: python setup_credentials.py")
    print("Or set GOOGLE_APPLICATION_CREDENTIALS environment variable")
    return False

def detect_text(path):
    """Detects text in the file."""
    if not setup_credentials():
        return

    try:
        from google.cloud import vision
        client = vision.ImageAnnotatorClient()
    except Exception as e:
        print(f"Error creating Vision client: {e}")
        print("Make sure you have valid Google Cloud credentials")
        return

    if not os.path.exists(path):
        print(f"Error: Image file '{path}' not found")
        return

    with open(path, "rb") as image_file:
        content = image_file.read()

    image = vision.Image(content=content)

    try:
        response = client.text_detection(image=image)
        texts = response.text_annotations

        if response.error.message:
            raise Exception(
                f"{response.error.message}\n"
                "For more info on error messages, check: "
                "https://cloud.google.com/apis/design/errors"
            )

        if not texts:
            print("No text detected in the image.")
            return

        print("Detected text:")
        print("=" * 50)

        # First annotation contains all detected text
        if texts:
            print(f"Full text:\n{texts[0].description}")
            print("\n" + "=" * 50)

        # Individual text annotations with bounding boxes
        print("Individual text elements:")
        for i, text in enumerate(texts[1:], 1):  # Skip the first one (full text)
            print(f"\n{i}. \"{text.description}\"")

            vertices = [
                f"({vertex.x},{vertex.y})" for vertex in text.bounding_poly.vertices
            ]
            print(f"   Bounds: {', '.join(vertices)}")

    except Exception as e:
        print(f"Error during text detection: {e}")

if __name__ == "__main__":
    image_path = 'jar.jpg'
    if len(sys.argv) > 1:
        image_path = sys.argv[1]

    detect_text(image_path)
