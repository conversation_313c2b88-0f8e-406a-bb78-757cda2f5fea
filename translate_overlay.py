#!/usr/bin/env python3
"""
Create Google Translate-style overlay that hides original text with translations.
"""
import json
import sys
import os
import math
from pathlib import Path

def install_dependencies():
    """Install required packages for image processing."""
    try:
        from PIL import Image, ImageDraw, ImageFont
        import numpy as np
    except ImportError:
        print("Installing required packages...")
        os.system("pip install pillow numpy")
        from PIL import Image, ImageDraw, ImageFont
        import numpy as np

def get_text_color(image, bbox):
    """Analyze the original text color to choose appropriate overlay color."""
    from PIL import Image
    import numpy as np
    
    # Extract the region
    x_coords = [point[0] for point in bbox]
    y_coords = [point[1] for point in bbox]
    x_min, x_max = max(0, min(x_coords)), min(image.width, max(x_coords))
    y_min, y_max = max(0, min(y_coords)), min(image.height, max(y_coords))
    
    if x_max <= x_min or y_max <= y_min:
        return (0, 0, 0)  # Default to black
    
    # Get the region
    region = image.crop((x_min, y_min, x_max, y_max))
    
    # Convert to numpy array
    region_array = np.array(region)
    
    # Calculate average brightness
    if len(region_array.shape) == 3:
        brightness = np.mean(region_array)
    else:
        brightness = np.mean(region_array)
    
    # Return black for light backgrounds, white for dark backgrounds
    return (0, 0, 0) if brightness > 127 else (255, 255, 255)

def get_background_color(image, bbox, padding=5):
    """Get the dominant background color around the text."""
    from PIL import Image
    import numpy as np
    
    x_coords = [point[0] for point in bbox]
    y_coords = [point[1] for point in bbox]
    x_min, x_max = min(x_coords), max(x_coords)
    y_min, y_max = min(y_coords), max(y_coords)
    
    # Expand the region slightly to get background
    x_min = max(0, x_min - padding)
    y_min = max(0, y_min - padding)
    x_max = min(image.width, x_max + padding)
    y_max = min(image.height, y_max + padding)
    
    if x_max <= x_min or y_max <= y_min:
        return (255, 255, 255)  # Default to white
    
    # Get the region
    region = image.crop((x_min, y_min, x_max, y_max))
    region_array = np.array(region)
    
    # Get the most common color (simplified approach)
    if len(region_array.shape) == 3:
        # Reshape to list of pixels
        pixels = region_array.reshape(-1, region_array.shape[-1])
        # Calculate mean color
        mean_color = np.mean(pixels, axis=0)
        return tuple(mean_color.astype(int))
    else:
        mean_color = np.mean(region_array)
        return (int(mean_color), int(mean_color), int(mean_color))

def calculate_font_size(bbox, text, max_font_size=50):
    """Calculate appropriate font size to fit the bounding box."""
    width = max([point[0] for point in bbox]) - min([point[0] for point in bbox])
    height = max([point[1] for point in bbox]) - min([point[1] for point in bbox])
    
    # Estimate font size based on bounding box dimensions
    # Rough estimation: font_size ≈ height * 0.8
    estimated_size = int(height * 0.7)
    
    # Also consider width constraint
    char_width_ratio = 0.6  # Approximate character width to height ratio
    width_based_size = int(width / (len(text) * char_width_ratio))
    
    # Use the smaller of the two estimates, but cap at max_font_size
    font_size = min(estimated_size, width_based_size, max_font_size)
    
    return max(8, font_size)  # Minimum font size of 8

def get_font(size):
    """Get a font of the specified size."""
    from PIL import ImageFont
    
    # Try to use a good font, fall back to default if not available
    font_paths = [
        "/usr/share/fonts/truetype/dejavu/DejaVuSans-Bold.ttf",
        "/usr/share/fonts/truetype/liberation/LiberationSans-Bold.ttf",
        "/System/Library/Fonts/Arial.ttf",  # macOS
        "C:/Windows/Fonts/arial.ttf",  # Windows
    ]
    
    for font_path in font_paths:
        if os.path.exists(font_path):
            try:
                return ImageFont.truetype(font_path, size)
            except:
                continue
    
    # Fall back to default font
    try:
        return ImageFont.load_default()
    except:
        return ImageFont.load_default()

def create_translation_overlay(image_path: str, json_path: str, output_path: str = None, style: str = "seamless"):
    """Create a translation overlay that hides original text.

    Args:
        style: 'seamless' (match background) or 'highlight' (colored background)
    """

    # Install dependencies if needed
    install_dependencies()
    from PIL import Image, ImageDraw, ImageFont
    import numpy as np

    # Load the OCR results
    if not os.path.exists(json_path):
        print(f"Error: JSON file '{json_path}' not found")
        return

    with open(json_path, 'r', encoding='utf-8') as f:
        ocr_data = json.load(f)

    # Load the image
    if not os.path.exists(image_path):
        print(f"Error: Image file '{image_path}' not found")
        return

    image = Image.open(image_path).convert('RGB')

    # Create a copy for overlay
    overlay_image = image.copy()
    draw = ImageDraw.Draw(overlay_image)

    print(f"Processing {len(ocr_data.get('elements', []))} text elements...")
    print(f"Style: {style}")

    # Process each text element
    for i, element in enumerate(ocr_data.get('elements', [])):
        bbox = element['bounding_box']
        original_text = element['corrected_text']
        translation = element['english_translation']
        confidence = element.get('confidence', 'unknown')
        show_translation = element.get('show_translation', True)

        # Skip if show_translation is False or translation is empty
        if not show_translation or not translation.strip():
            continue

        # Skip if translation is the same as original (likely already in English)
        if original_text.lower().strip() == translation.lower().strip():
            continue

        print(f"  {i+1}. '{original_text}' → '{translation}' ({confidence})")

        # Calculate bounding box dimensions
        x_coords = [point[0] for point in bbox]
        y_coords = [point[1] for point in bbox]
        x_min, x_max = min(x_coords), max(x_coords)
        y_min, y_max = min(y_coords), max(y_coords)

        width = x_max - x_min
        height = y_max - y_min

        if width <= 0 or height <= 0:
            continue

        # Calculate font size
        font_size = calculate_font_size(bbox, translation)
        font = get_font(font_size)

        # Create padding based on font size
        padding = max(2, font_size // 10)
        rect_x1 = x_min - padding
        rect_y1 = y_min - padding
        rect_x2 = x_max + padding
        rect_y2 = y_max + padding

        # Choose background and text colors based on style
        if style == "highlight":
            # Use colored background for translations
            confidence_colors = {
                'high': (144, 238, 144),    # Light green
                'medium': (255, 255, 224),  # Light yellow
                'low': (255, 182, 193),     # Light pink
                'unknown': (211, 211, 211)  # Light gray
            }
            bg_color = confidence_colors.get(confidence, (211, 211, 211))
            text_color = (0, 0, 0)  # Black text
        else:
            # Seamless style - match background
            bg_color = get_background_color(image, bbox, padding)
            text_color = get_text_color(image, bbox)

        # Draw background rectangle to cover original text
        draw.rectangle([rect_x1, rect_y1, rect_x2, rect_y2], fill=bg_color)

        # Calculate text position (center of bounding box)
        # Get text dimensions
        try:
            text_bbox = draw.textbbox((0, 0), translation, font=font)
            text_width = text_bbox[2] - text_bbox[0]
            text_height = text_bbox[3] - text_bbox[1]
        except:
            # Fallback for older PIL versions
            text_width, text_height = draw.textsize(translation, font=font)

        # Center the text in the bounding box
        text_x = x_min + (width - text_width) // 2
        text_y = y_min + (height - text_height) // 2

        # Ensure text stays within image bounds
        text_x = max(0, min(text_x, image.width - text_width))
        text_y = max(0, min(text_y, image.height - text_height))

        # Add text outline for better readability if needed
        if style == "seamless" and abs(sum(bg_color) - sum(text_color)) < 300:
            # Add text outline for low contrast situations
            outline_color = (255, 255, 255) if sum(text_color) < 400 else (0, 0, 0)
            for adj_x, adj_y in [(-1,-1), (-1,0), (-1,1), (0,-1), (0,1), (1,-1), (1,0), (1,1)]:
                draw.text((text_x + adj_x, text_y + adj_y), translation,
                         fill=outline_color, font=font)

        # Draw the translation text
        draw.text((text_x, text_y), translation, fill=text_color, font=font)
    
    # Save the result
    if output_path is None:
        output_path = f"{Path(image_path).stem}_translated.{Path(image_path).suffix[1:]}"
    
    overlay_image.save(output_path, quality=95)
    print(f"\nTranslation overlay saved to: {output_path}")
    
    # Display summary
    detected_lang = ocr_data.get('detected_language', 'Unknown')
    total_elements = ocr_data.get('total_elements', 0)
    cost_info = ocr_data.get('cost_breakdown', {})
    total_cost = cost_info.get('total_usd', 0)
    
    print(f"\nSummary:")
    print(f"  Original language: {detected_lang}")
    print(f"  Elements processed: {total_elements}")
    print(f"  Total cost: ${total_cost:.6f}")
    print(f"  Original text: {ocr_data.get('full_corrected_text', '')}")
    print(f"  Translation: {ocr_data.get('full_english_translation', '')}")
    
    return output_path

def create_side_by_side_comparison(image_path: str, translated_path: str, output_path: str = None):
    """Create a side-by-side comparison of original and translated images."""
    from PIL import Image
    
    original = Image.open(image_path)
    translated = Image.open(translated_path)
    
    # Create side-by-side image
    total_width = original.width + translated.width
    max_height = max(original.height, translated.height)
    
    comparison = Image.new('RGB', (total_width, max_height), (255, 255, 255))
    comparison.paste(original, (0, 0))
    comparison.paste(translated, (original.width, 0))
    
    if output_path is None:
        output_path = f"{Path(image_path).stem}_comparison.jpg"
    
    comparison.save(output_path, quality=95)
    print(f"Comparison image saved to: {output_path}")
    
    return output_path

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python translate_overlay.py <json_file> [image_file] [output_file] [--style=seamless|highlight]")
        print("Example: python translate_overlay.py jar_ocr_results.json jar.jpg jar_translated.jpg")
        print("Styles:")
        print("  seamless  - Match background color (Google Translate style)")
        print("  highlight - Colored backgrounds based on confidence")
        sys.exit(1)

    json_file = sys.argv[1]

    # Parse style argument
    style = "seamless"
    args = [arg for arg in sys.argv if not arg.startswith('--')]
    style_args = [arg for arg in sys.argv if arg.startswith('--style=')]
    if style_args:
        style = style_args[0].split('=')[1]

    # Try to infer image file from JSON filename
    if len(args) > 2:
        image_file = args[2]
    else:
        # Infer from JSON filename
        json_stem = Path(json_file).stem
        if json_stem.endswith('_ocr_results'):
            image_stem = json_stem[:-12]  # Remove '_ocr_results'
            # Look for common image extensions
            for ext in ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']:
                potential_image = f"{image_stem}{ext}"
                if os.path.exists(potential_image):
                    image_file = potential_image
                    break
            else:
                print(f"Could not find image file for {json_file}")
                print("Please specify the image file as the second argument")
                sys.exit(1)
        else:
            print("Could not infer image filename. Please specify it as the second argument")
            sys.exit(1)

    output_file = args[3] if len(args) > 3 else None

    print(f"Creating translation overlay...")
    print(f"Input: {image_file}")
    print(f"OCR data: {json_file}")
    print(f"Style: {style}")

    # Create both styles
    print("\n--- Creating seamless overlay ---")
    seamless_path = create_translation_overlay(image_file, json_file,
                                             f"{Path(image_file).stem}_seamless.jpg", "seamless")

    print("\n--- Creating highlight overlay ---")
    highlight_path = create_translation_overlay(image_file, json_file,
                                              f"{Path(image_file).stem}_highlight.jpg", "highlight")

    if seamless_path and highlight_path:
        # Create comparisons
        print("\n--- Creating comparisons ---")
        create_side_by_side_comparison(image_file, seamless_path,
                                     f"{Path(image_file).stem}_seamless_comparison.jpg")
        create_side_by_side_comparison(image_file, highlight_path,
                                     f"{Path(image_file).stem}_highlight_comparison.jpg")

        print(f"\n✅ Translation overlays complete!")
        print(f"   Seamless style: {seamless_path}")
        print(f"   Highlight style: {highlight_path}")
        print(f"   Comparisons: {Path(image_file).stem}_*_comparison.jpg")
